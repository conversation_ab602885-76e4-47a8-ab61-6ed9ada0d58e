'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import Button from '@/components/ui/Button';
import Slider from '@/components/common/Slider';
interface Course {
  id: string;
  title: string;
  price: string;
  instructor: string;
  instructorImage: string;
  courseImage: string;
}
interface Instructor {
  id: string;
  name: string;
  title: string;
  image: string;
}
interface Article {
  id: string;
  title: string;
  excerpt: string;
  image: string;
}
const HomePage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('Tất cả');
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const categories = [
    'Tất cả',
    'Dinh dưỡng',
    '<PERSON><PERSON><PERSON>ậ<PERSON>',
    'Tâm lý học',
    '<PERSON><PERSON><PERSON> Động',
    'Y học cổ truyền',
  ];
  const courses: Course[] = [
    {
      id: '1',
      title: '<PERSON><PERSON> độ ăn cho trẻ suy dinh dưỡng dưới 5 tuổi',
      price: '400.000 đ',
      instructor: 'ThS Trần Th<PERSON>ơ<PERSON>',
      instructorImage: '/images/img_image_7.png',
      courseImage: '/images/img_image_204x294.png',
    },
    {
      id: '2',
      title: '12 ngày học thanh nhạc cơ bản dành cho người lớn',
      price: '600.000 đ',
      instructor: 'GV Lý Bảo Long',
      instructorImage: '/images/img_image_9.png',
      courseImage: '/images/img_image_8.png',
    },
    {
      id: '3',
      title: '15 bài tập Yoga nâng cao sức khỏe tại nhà cho học sinh, sinh viên',
      price: '800.000 đ',
      instructor: 'HLV Nguyễn Duy Đông',
      instructorImage: '/images/img_image_11.png',
      courseImage: '/images/img_image_10.png',
    },
    {
      id: '4',
      title: '15 ngày làm quen và thực hành về Popping dance cơ bản',
      price: '600.000 đ',
      instructor: 'Dancer Nguyễn Khắc Quân',
      instructorImage: '/images/img_image_13.png',
      courseImage: '/images/img_image_12.png',
    },
    {
      id: '5',
      title: '15 ngày làm quen với Hiphop Dance cho người mới bắt đầu',
      price: '600.000 đ',
      instructor: 'Dancer Nguyễn Khắc Quân',
      instructorImage: '/images/img_image_13.png',
      courseImage: '/images/img_image_14.png',
    },
    {
      id: '6',
      title: 'Biện pháp phát triển từ, cụm từ và câu cho trẻ em',
      price: '600.000 đ',
      instructor: 'ThS Lê Thị Tố Uyên',
      instructorImage: '/images/img_image_16.png',
      courseImage: '/images/img_image_15.png',
    },
    {
      id: '7',
      title: 'Bí quyết chăm sóc khi bị viêm mũi dị ứng – Hướng dẫn từ chuyên gia',
      price: '600.000 đ',
      instructor: 'BS Nguyễn Thị Vân Anh',
      instructorImage: '/images/img_image_18.png',
      courseImage: '/images/img_image_17.png',
    },
    {
      id: '8',
      title: 'Các bài tập giảm mỡ tại văn phòng chỉ 30 phút mỗi ngày',
      price: '400.000 đ',
      instructor: 'HLV Nguyễn Mạnh Trường',
      instructorImage: '/images/img_image_20.png',
      courseImage: '/images/img_image_19.png',
    },
  ];
  const instructors: Instructor[] = [
    {
      id: '1',
      name: 'GV Bùi Tuấn Đạt',
      title: 'Giáo viên chuyên nghiệp',
      image: '/images/img_image_294x280.png',
    },
    {
      id: '2',
      name: 'GV Bùi Thị Hậu',
      title: 'Giáo viên chuyên nghiệp',
      image: '/images/img_image_21.png',
    },
    {
      id: '3',
      name: 'GV Đỗ Văn Thắng',
      title: 'Giáo viên chuyên nghiệp',
      image: '/images/img_image_22.png',
    },
    {
      id: '4',
      name: 'GV Nguyễn Hồng Nhật',
      title: 'Giáo viên chuyên nghiệp',
      image: '/images/img_image_23.png',
    },
    {
      id: '5',
      name: 'ThS Nguyễn Lê',
      title: 'Giáo viên chuyên nghiệp',
      image: '/images/img_image_24.png',
    },
    {
      id: '6',
      name: 'GV Nguyễn Thị Lan Hương',
      title: 'Giáo viên chuyên nghiệp',
      image: '/images/img_image_25.png',
    },
  ];
  const articles: Article[] = [
    {
      id: '1',
      title: 'Bí Quyết Giảm Béo & Làm Đẹp Tự Nhiên Với Giác Hơi Hiệu Quả',
      excerpt:
        'Bạn đang tìm kiếm một phương pháp giảm cân an toàn, không cần dùng thuốc và mang lại hiệu quả bền vững? Giác hơi, một liệu pháp Y học cổ truyền, chính là giải pháp tối ưu giúp bạn sở hữu vóc dáng thon gọn và làn da rạng rỡ một cách tự nhiên. Giác […]',
      image: '/images/img_image_256x400.png',
    },
    {
      id: '2',
      title: 'Tuyệt Chiêu Giao Tiếp: Tái Hiện Cảm Xúc Ngọt Ngào Như Thuở Ban Đầu',
      excerpt:
        '"Tình yêu lúc mới bắt đầu luôn dễ dàng, nhưng để giữ mãi cảm xúc ấy theo năm tháng lại là một nghệ thuật." Bạn có bao giờ nhớ lại những ngày đầu bên nhau – khi mỗi tin nhắn, mỗi cái chạm tay đều khiến tim đập rộn ràng? Theo thời gian, công việc, […]',
      image: '/images/img_image_26.png',
    },
    {
      id: '3',
      title: 'LỄ HỘI RA MẮT THÁNG 8 – CHỦ ĐỘNG HỌC, CHỦ ĐỘNG SỐNG KHỎE',
      excerpt:
        "OM'E Việt Nam xin gửi lời chào trân trọng tới những người đã và đang lựa chọn sống một cách chủ động – chủ động học hỏi, chủ động chăm sóc bản thân và chủ động xây dựng một cuộc sống khỏe mạnh từ gốc. Trong thế giới hiện đại, nơi tốc độ và […]",
      image: '/images/img_image_27.png',
    },
    {
      id: '4',
      title: "LIỆU PHÁP GIÁC HƠI: Học Trị Liệu & Phục Hồi Sức Khỏe Toàn Diện Cùng OM'E Việt Nam",
      excerpt:
        'Giác Hơi – Bí Quyết Khai Thông Khí Huyết, Cải Thiện Thân Tâm Theo Y học cổ truyền Á Đông, sức khỏe bắt nguồn từ sự lưu thông khí huyết. Khi khí huyết ứ trệ, tắc nghẽn, cơ thể sẽ sinh ra bệnh tật. Liệu pháp giác hơi sử dụng sức hút chân không để […]',
      image: '/images/img_image_28.png',
    },
    {
      id: '5',
      title: 'Sức Khỏe Mạn Tính: Hiểu Rõ Để Sống Khỏe Mỗi Ngày',
      excerpt:
        'Bạn có bao giờ tự hỏi vì sao các bệnh mạn tính như tiểu đường, tim mạch, hay huyết áp cao ngày càng trở nên phổ biến? Không chỉ là câu chuyện của gen di truyền hay tuổi tác, mà những thói quen hàng ngày của chúng ta đóng vai trò then chốt trong việc […]',
      image: '/images/img_image_29.png',
    },
    {
      id: '6',
      title: "OM'E VIỆT NAM PHỐI HỢP CÙNG ĐẠI HỌC HÀNG HẢI VIỆT NAM TỔ CHỨC THÀNH CÔNG WORKSHOP",
      excerpt:
        "Tối ngày 23/07/2025, được sự quan tâm chỉ đạo của Đảng ủy và Ban Giám hiệu nhà trường, Đoàn Thanh niên – Hội Sinh viên Trường Đại học Hàng Hải Việt Nam đã phối hợp cùng Trung tâm chăm sóc sức khỏe chủ động OM'E Việt Nam tổ chức thành công buổi Workshop giao lưu […]",
      image: '/images/img_image_30.png',
    },
  ];
  const testimonials = [
    {
      id: '1',
      name: 'Nguyễn Hương Trà',
      title: 'Điều Dưỡng Viên',
      image: '/images/img_image_80x80.png',
      content:
        'Phương pháp học đơn giản, có thể xem đi xem lại nên các động tác không lo sai kỹ thuật, giảng viên dạy dễ hiểu lại còn chia theo từng chủ đề lựa chọn nữa, mình mới học 2 khoá nhưng khá hài lòng',
    },
    {
      id: '2',
      name: 'Hoàng Minh',
      title: 'Học viên',
      image: '/images/img_image_80x80.png',
      content:
        'Ngoài Video học trực tuyến thì còn có cả giáo trình bản mềm đi kèm nữa nên cũng khá là chi tiết cho một người mới như mình, giảng viên dạy cũng dễ hiểu và dễ thực hành theo, cảm ơn cô và VMC',
    },
  ];
  const partners = [
    '/images/img_image_48x120.png',
    '/images/img_image_31.png',
    '/images/img_image_32.png',
    '/images/img_image_33.png',
    '/images/img_image_34.png',
  ];
  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category);
  };
  const handleCourseClick = (courseId: string) => {
    console.log('Course clicked:', courseId);
  };
  const handleInstructorClick = (instructorId: string) => {
    console.log('Instructor clicked:', instructorId);
  };
  const handleArticleClick = (articleId: string) => {
    console.log('Article clicked:', articleId);
  };
  return (
    <>
      <div className="w-full bg-global-12 overflow-x-hidden">
        {/* Hero Slider Section */}
        <section className="w-full">
          <Slider autoPlay autoPlayInterval={5000}>
            <div className="relative w-full h-full">
              <Image
                src="/images/img_image_750x1920.png"
                alt="Hero slide 1"
                fill
                className="object-cover"
                priority
              />
              <div className="absolute inset-0 bg-[url('/images/img_image_38.png')] bg-cover bg-center" />
              <div className="absolute top-1/2 right-4 sm:right-8 md:right-16 lg:right-[80px] transform -translate-y-1/2 flex flex-col gap-4">
                <div className="w-[80px] h-[80px] bg-slider-1 rounded-[38px]" />
                <div className="relative w-[86px] h-[86px]">
                  <div className="w-full h-full bg-[#2196f3] rounded-[34px]" />
                  <Image
                    src="/images/img_image_30x32.png"
                    alt="Icon"
                    width={32}
                    height={30}
                    className="absolute bottom-4 left-[18px]"
                  />
                </div>
                <div className="w-[80px] h-[80px] bg-slider-2 rounded-[38px]" />
                <div className="relative w-[86px] h-[86px]">
                  <div className="w-full h-full bg-[#f44336] rounded-[34px]" />
                  <Image
                    src="/images/img_image_39.png"
                    alt="Icon"
                    width={32}
                    height={32}
                    className="absolute bottom-4 left-[18px]"
                  />
                </div>
              </div>
            </div>
            <div className="relative w-full h-full">
              <Image
                src="/images/img_image_37.png"
                alt="Hero slide 2"
                fill
                className="object-cover"
              />
            </div>
          </Slider>
        </section>
        {/* Main Content */}
        <div className="w-full flex flex-col items-center">
          {/* Health Solutions Section */}
          <section className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20">
            <div className="bg-global-12 rounded-[50px] p-6 sm:p-8 md:p-12 lg:p-[50px] shadow-lg">
              <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
                <div className="w-full lg:w-[68%] flex flex-col gap-6 lg:gap-8">
                  <div className="flex flex-col gap-2">
                    <h1 className="text-[24px] sm:text-[30px] md:text-[35px] lg:text-[40px] font-bold leading-tight text-global-3">
                      Giải Pháp Cho Sức Khỏe Bạn
                    </h1>
                    <p className="text-sm sm:text-base text-global-8 leading-relaxed">
                      Chúng tôi hân hạnh cung cấp tới toàn thể quý cộng đồng những dòng giải pháp
                      hữu ích giúp mọi người có thể chủ động nâng cao sức khỏe về cả thể chất lẫn
                      tinh thần của mình
                    </p>
                  </div>
                  <Button
                    variant="primary"
                    size="md"
                    className="bg-global-3 text-global-11 rounded-[14px] px-4 py-2 w-fit"
                    rightIcon="/images/img_arrow_right.svg"
                  >
                    XEM TẤT CẢ
                  </Button>
                </div>
                <div className="w-full lg:w-[32%] flex flex-col gap-4">
                  <div className="flex gap-4">
                    <Image
                      src="/images/img_image_192x292.png"
                      alt="Y Học Cổ Truyền"
                      width={292}
                      height={192}
                      className="w-1/2 rounded-[14px]"
                    />
                    <Image
                      src="/images/img_image_1.png"
                      alt="Giáo Dục Đặc Biệt"
                      width={292}
                      height={192}
                      className="w-1/2 rounded-[14px]"
                    />
                  </div>
                  <div className="flex justify-between text-center">
                    <h3 className="text-lg sm:text-xl md:text-2xl font-semibold text-global-3">
                      Y Học Cổ Truyền
                    </h3>
                    <h3 className="text-lg sm:text-xl md:text-2xl font-semibold text-global-3">
                      Giáo Dục Đặc Biệt
                    </h3>
                  </div>
                </div>
              </div>
              <div className="mt-8 lg:mt-12">
                <div className="flex flex-wrap gap-4 justify-center">
                  <Image
                    src="/images/img_image_192x296.png"
                    alt="Dinh Dưỡng"
                    width={296}
                    height={192}
                    className="w-full sm:w-[48%] md:w-[23%] rounded-[14px]"
                  />
                  <Image
                    src="/images/img_image_2.png"
                    alt="Cắm Hoa"
                    width={296}
                    height={192}
                    className="w-full sm:w-[48%] md:w-[23%] rounded-[14px]"
                  />
                  <Image
                    src="/images/img_image_3.png"
                    alt="Yoga"
                    width={296}
                    height={192}
                    className="w-full sm:w-[48%] md:w-[23%] rounded-[14px]"
                  />
                  <Image
                    src="/images/img_image_4.png"
                    alt="Âm Nhạc"
                    width={296}
                    height={192}
                    className="w-full sm:w-[48%] md:w-[23%] rounded-[14px]"
                  />
                </div>
                <div className="flex flex-wrap justify-between mt-4 text-center">
                  <h3 className="w-full sm:w-[48%] md:w-[23%] text-lg sm:text-xl md:text-2xl font-semibold text-global-3">
                    Dinh Dưỡng
                  </h3>
                  <h3 className="w-full sm:w-[48%] md:w-[23%] text-lg sm:text-xl md:text-2xl font-semibold text-global-3">
                    Cắm Hoa
                  </h3>
                  <h3 className="w-full sm:w-[48%] md:w-[23%] text-lg sm:text-xl md:text-2xl font-semibold text-global-3">
                    Yoga
                  </h3>
                  <h3 className="w-full sm:w-[48%] md:w-[23%] text-lg sm:text-xl md:text-2xl font-semibold text-global-3">
                    Âm Nhạc
                  </h3>
                </div>
              </div>
            </div>
          </section>
          {/* Features Section */}
          <section className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16">
            <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
              <div className="w-full lg:w-1/2">
                <Image
                  src="/images/img_image_500x500.png"
                  alt="OM'E Features"
                  width={500}
                  height={500}
                  className="w-full h-auto rounded-lg"
                />
              </div>
              <div className="w-full lg:w-1/2 flex flex-col gap-8">
                <div className="flex flex-col gap-4">
                  <Image src="/images/img_image_32x32.png" alt="Icon" width={32} height={32} />
                  <h3 className="text-xl sm:text-2xl font-semibold text-global-2">
                    Phổ Cập Kiến Thức
                  </h3>
                  <p className="text-sm sm:text-base text-global-1 leading-relaxed">
                    Trao gửi kiến thức hữu ích với tính thực tiễn nhằm nâng cao nhận thức mỗi người
                    về tầm quan trọng của chủ động chăm sóc sức khỏe
                  </p>
                </div>
                <div className="flex flex-col gap-4">
                  <Image src="/images/img_image_5.png" alt="Icon" width={32} height={32} />
                  <h3 className="text-xl sm:text-2xl font-semibold text-global-2">
                    Hỗ Trợ Học Tập 24/7
                  </h3>
                  <p className="text-sm sm:text-base text-global-1 leading-relaxed">
                    Tới với OM'E trong suốt vòng đời học tập của mình chỉ cần có thắc mắc học viên
                    sẽ được bộ phận chăm sóc riêng hỗ trợ 24/7, đảm bảo hiệu quả khóa học ở mức tối
                    đa
                  </p>
                </div>
                <div className="flex flex-col gap-4">
                  <Image src="/images/img_image_6.png" alt="Icon" width={32} height={32} />
                  <h3 className="text-xl sm:text-2xl font-semibold text-global-2">
                    Chuyên Viên Hàng Đầu
                  </h3>
                  <p className="text-sm sm:text-base text-global-1 leading-relaxed">
                    Liên kết với các chuyên gia hàng đầu, OM'E tự hào với hệ thống giảng viên có
                    chuyên môn cao trong các lĩnh vực, sẵn sàng có thể tư vấn cho học viên mọi vấn
                    đề về sức khỏe
                  </p>
                </div>
              </div>
            </div>
          </section>
          {/* Hot Courses Section */}
          <section className="w-full bg-global-11 py-8 sm:py-12 md:py-16">
            <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex flex-col items-center gap-6 sm:gap-8">
                <Button
                  variant="primary"
                  size="sm"
                  className="bg-global-12 text-button-2 shadow-[0px_4px_30px_#eb725744]"
                  leftIcon="/images/img_frame.svg"
                >
                  KHÓA HỌC
                </Button>
                <h2 className="text-[20px] sm:text-[24px] md:text-[30px] font-bold text-global-4 text-center">
                  Các khóa học Hot nhất 2025
                </h2>
                <div className="bg-global-10 rounded-[14px] p-2 flex flex-wrap gap-2 justify-center">
                  {categories.map((category) => (
                    <button
                      key={category}
                      onClick={() => handleCategoryClick(category)}
                      className={`px-3 py-1.5 rounded-[16px] text-sm font-bold transition-all duration-200 ${
                        selectedCategory === category
                          ? 'bg-button-1 text-global-11 shadow-[0px_1px_2px_#0000000c]'
                          : 'text-global-7 hover:bg-button-1 hover:text-global-11'
                      }`}
                    >
                      {category}
                    </button>
                  ))}
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 w-full">
                  {courses.slice(0, 8).map((course) => (
                    <div
                      key={course.id}
                      className="bg-global-12 rounded-[24px] shadow-[0px_4px_6px_#00000019] overflow-hidden cursor-pointer hover:scale-105 transition-transform duration-200"
                      onClick={() => handleCourseClick(course.id)}
                    >
                      <Image
                        src={course.courseImage}
                        alt={course.title}
                        width={294}
                        height={204}
                        className="w-full h-[150px] sm:h-[180px] md:h-[204px] object-cover"
                      />
                      <div className="p-4 flex flex-col gap-3">
                        <h3 className="text-base sm:text-lg md:text-xl font-bold text-global-4 line-clamp-2 leading-tight">
                          {course.title}
                        </h3>
                        <div className="flex justify-end">
                          <span className="bg-global-4 text-global-11 text-sm font-bold px-3 py-1 rounded-[14px]">
                            {course.price}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Image
                              src={course.instructorImage}
                              alt={course.instructor}
                              width={32}
                              height={32}
                              className="w-8 h-8 rounded-full"
                            />
                            <span className="text-sm text-global-5 truncate">
                              {course.instructor}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-sm text-global-9">Chi tiết</span>
                            <Image
                              src="/images/img_arrow_right_yellow_900.svg"
                              alt="Arrow"
                              width={24}
                              height={24}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <Button
                  variant="outline"
                  size="md"
                  className="border border-global-4 text-global-4 bg-global-12 hover:bg-global-4 hover:text-global-11"
                >
                  Xem thêm
                </Button>
              </div>
            </div>
          </section>
          {/* About OM'E Section */}
          <section className="w-full bg-global-7 py-8 sm:py-12 md:py-16">
            <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
                <div className="w-full lg:w-1/2 flex flex-col gap-6">
                  <h2 className="text-[24px] sm:text-[30px] md:text-[40px] font-bold text-global-3">
                    Về OM'E Việt Nam
                  </h2>
                  <p className="text-sm sm:text-base text-global-5 leading-relaxed">
                    Chúng tôi sinh ra bởi sự thôi thúc mang tới sức khỏe và giá trị thực sự từ bên
                    trong mỗi người Việt
                  </p>
                  <div className="flex gap-2">
                    <Image src="/images/img_image_24x32.png" alt="Quote" width={32} height={24} />
                    <p className="text-sm sm:text-base font-bold text-global-6 leading-relaxed">
                      Trong suốt chặng đường phát triển, OM'E thật may mắn được kết hợp với các
                      chuyên gia có cùng tầm nhìn, từ đó đóng góp rất nhiều cho sự nâng cao nhận
                      thức của mỗi người mỗi nhà trong việc chủ động chăm sóc sức khỏe
                    </p>
                  </div>
                  <p className="text-sm sm:text-base text-global-5 leading-relaxed">
                    OM'E rất mong mỗi học viên khi học cùng chúng tôi hãy mang theo tinh thần nghiêm
                    túc, sự kiên trì và cam kết thực nghiệm theo nội dung được giảng dạy để đảm bảo
                    mỗi khóa học thực hiện được nhiệm vụ trao đi giá trị cho người học của chính nó!
                  </p>
                  <Button
                    variant="primary"
                    size="md"
                    className="bg-global-3 text-global-11 rounded-[14px] w-fit"
                    rightIcon="/images/img_arrow_right.svg"
                  >
                    ĐỌC THÊM
                  </Button>
                </div>
                <div className="w-full lg:w-1/2 relative">
                  <Image
                    src="/images/img_image_508x960.png"
                    alt="OM'E Statistics"
                    width={960}
                    height={508}
                    className="w-full h-auto rounded-lg"
                  />
                  <div className="absolute top-4 left-4 flex flex-col gap-4">
                    <div className="bg-global-9 rounded-[24px] p-4 text-global-11">
                      <div className="flex items-end gap-2">
                        <span className="text-2xl sm:text-3xl font-bold">1452</span>
                        <div className="flex flex-col text-xs sm:text-sm">
                          <span>Chuyên gia liên kết cùng OM'E</span>
                          <span>Phương pháp chuẩn quốc tế</span>
                        </div>
                      </div>
                    </div>
                    <div className="bg-global-9 rounded-[24px] p-4 text-global-11">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl sm:text-3xl font-bold">+13000</span>
                        <span className="text-xs sm:text-sm">Học viên trên toàn quốc</span>
                      </div>
                    </div>
                    <div className="bg-global-9 rounded-[24px] p-4 text-global-11">
                      <div className="flex flex-col gap-1">
                        <div className="flex items-center gap-3">
                          <span className="text-2xl sm:text-3xl font-bold">10</span>
                          <span className="text-xs sm:text-sm">Năm kinh nghiệm</span>
                        </div>
                        <span className="text-xs sm:text-sm">Phương pháp online tiện lợi</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          {/* Instructors Section */}
          <section className="w-full bg-global-12 py-8 sm:py-12 md:py-16">
            <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex flex-col items-center gap-8">
                <Button
                  variant="primary"
                  size="sm"
                  className="bg-global-12 text-button-2 shadow-[0px_4px_30px_#eb725744]"
                  leftIcon="/images/img_frame.svg"
                >
                  GIÁO VIÊN
                </Button>
                <div className="text-center">
                  <h2 className="text-[24px] sm:text-[30px] md:text-[36px] font-bold text-global-4 mb-2">
                    Đội ngũ giáo viên xuất sắc
                  </h2>
                  <p className="text-sm sm:text-base text-global-5">
                    Đội ngũ giáo viên giàu kinh nghiệm, tận tâm và chuyên nghiệp của chúng tôi
                  </p>
                </div>
                <div className="w-full overflow-x-auto">
                  <div className="flex gap-6 pb-4" style={{ width: 'max-content' }}>
                    {instructors.map((instructor) => (
                      <div
                        key={instructor.id}
                        className="flex-shrink-0 w-[282px] cursor-pointer hover:scale-105 transition-transform duration-200"
                        onClick={() => handleInstructorClick(instructor.id)}
                      >
                        <div className="bg-global-12 border border-gray-200 rounded-[24px] shadow-sm overflow-hidden">
                          <div className="relative">
                            <div className="w-16 h-16 bg-gradient-to-r from-[#d4af37cc] to-[#f0d68acc] rounded-[24px] absolute top-0 left-0 z-10" />
                            <div className="relative">
                              <Image
                                src={instructor.image}
                                alt={instructor.name}
                                width={280}
                                height={294}
                                className="w-full h-[294px] object-cover"
                              />
                              <div className="absolute inset-0 bg-gradient-to-r from-transparent to-[#4a306dcc] flex items-end">
                                <span className="text-sm text-global-8 p-4">Xem chi tiết</span>
                              </div>
                            </div>
                          </div>
                          <div className="p-6 text-center">
                            <div className="w-16 h-1 bg-gradient-to-r from-[#d4af37] to-[#f0d68a] mx-auto mb-5" />
                            <h3 className="text-lg sm:text-xl font-semibold text-global-3 mb-1">
                              {instructor.name}
                            </h3>
                            <p className="text-sm text-global-8">{instructor.title}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex gap-3">
                  {Array.from({ length: 8 }, (_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full ${
                        index === 3 ? 'bg-global-2' : 'bg-global-1'
                      }`}
                    />
                  ))}
                </div>
                <Button
                  variant="outline"
                  size="md"
                  className="border border-global-4 text-global-4 bg-global-12 hover:bg-global-4 hover:text-global-11 rounded-[20px]"
                >
                  Xem tất cả giáo viên →
                </Button>
              </div>
            </div>
          </section>
          {/* Articles Section */}
          <section className="w-full bg-global-12 py-8 sm:py-12 md:py-16">
            <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex flex-col items-center gap-8">
                <Button
                  variant="primary"
                  size="sm"
                  className="bg-global-12 text-button-2 shadow-[0px_4px_30px_#eb725744]"
                  leftIcon="/images/img_frame.svg"
                >
                  TIN TỨC
                </Button>
                <h2 className="text-[24px] sm:text-[30px] md:text-[36px] font-bold text-global-4 text-center">
                  Các bài viết mới nhất
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full">
                  {articles.map((article) => (
                    <div
                      key={article.id}
                      className="bg-global-12 rounded-lg shadow-[0px_1px_2px_#0000000c] overflow-hidden cursor-pointer hover:scale-105 transition-transform duration-200"
                      onClick={() => handleArticleClick(article.id)}
                    >
                      <Image
                        src={article.image}
                        alt={article.title}
                        width={400}
                        height={256}
                        className="w-full h-[200px] sm:h-[240px] md:h-[256px] object-cover"
                      />
                      <div className="p-6">
                        <h3 className="text-base sm:text-lg font-semibold text-global-4 mb-2 line-clamp-2">
                          {article.title}
                        </h3>
                        <p className="text-sm text-global-8 line-clamp-3 leading-relaxed">
                          {article.excerpt}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>
          {/* Testimonials Section */}
          <section className="w-full bg-global-8 py-8 sm:py-12 md:py-16">
            <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex flex-col items-center gap-8">
                <div className="text-center max-w-2xl">
                  <Image
                    src={testimonials[currentTestimonial].image}
                    alt={testimonials[currentTestimonial].name}
                    width={80}
                    height={80}
                    className="w-20 h-20 rounded-full mx-auto mb-4"
                  />
                  <Image
                    src="/images/img_frame_yellow_900.svg"
                    alt="Quote"
                    width={24}
                    height={24}
                    className="mx-auto mb-5"
                  />
                  <p className="text-sm sm:text-base text-global-5 leading-relaxed mb-5">
                    {testimonials[currentTestimonial].content}
                  </p>
                  <h4 className="text-base sm:text-lg font-semibold text-global-4 mb-1">
                    {testimonials[currentTestimonial].name}
                  </h4>
                  <p className="text-sm text-global-8">{testimonials[currentTestimonial].title}</p>
                </div>
                <div className="flex gap-3">
                  {testimonials.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentTestimonial(index)}
                      className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                        index === currentTestimonial ? 'bg-global-2' : 'bg-global-1'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </section>
          {/* Partners Section */}
          <section className="w-full bg-global-12 py-8 sm:py-12">
            <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-8">
                <h2 className="text-xl sm:text-2xl font-bold text-global-1">
                  Thương Hiệu Đồng Hành
                </h2>
              </div>
              <div className="flex flex-wrap justify-center items-center gap-8 sm:gap-12">
                {partners.map((partner, index) => (
                  <Image
                    key={index}
                    src={partner}
                    alt={`Partner ${index + 1}`}
                    width={120}
                    height={48}
                    className="h-12 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity duration-200"
                  />
                ))}
              </div>
            </div>
          </section>
          {/* CTA Section */}
          <section className="w-full bg-global-12 py-8 sm:py-12 md:py-16">
            <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
                <div className="w-full lg:w-1/2 flex flex-col gap-6">
                  <Button
                    variant="primary"
                    size="sm"
                    className="bg-global-12 text-button-2 shadow-[0px_4px_30px_#eb725744] w-fit"
                    leftIcon="/images/img_frame.svg"
                  >
                    Hợp Tác Cùng Chúng Tôi
                  </Button>
                  <h2 className="text-[20px] sm:text-[24px] md:text-[31px] font-black text-global-4 leading-tight">
                    Đồng hành cùng OM'E trao đi giá trị sức khỏe đích thực cho cộng đồng
                  </h2>
                  <div className="flex flex-col gap-2">
                    <p className="text-sm sm:text-base text-global-8">
                      Liên hệ ngay với chúng tôi qua số
                    </p>
                    <div className="flex items-center gap-2">
                      <span className="text-sm sm:text-base text-global-8">hotline</span>
                      <span className="text-sm sm:text-base font-bold text-global-10">
                        0966.000.643
                      </span>
                    </div>
                    <p className="text-sm sm:text-base text-global-8">để được cộng tác</p>
                    <p className="text-sm sm:text-base text-global-8">
                      Chúng tôi trân trọng và rất hân hạnh được đồng hành!
                    </p>
                  </div>
                  <Image
                    src="/images/img_image_102x182.png"
                    alt="Contact"
                    width={182}
                    height={102}
                    className="w-[182px] h-[102px] rounded-[50px]"
                  />
                </div>
                <div className="w-full lg:w-1/2">
                  <Image
                    src="/images/img_image_500x608.png"
                    alt="Partnership"
                    width={608}
                    height={500}
                    className="w-full h-auto rounded-lg"
                  />
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>

      <div className="w-full bg-[#d9d9d9]">
        <div className="w-full max-w-[1440px] mx-auto px-[28px] sm:px-[42px] lg:px-[56px] py-[36px] sm:py-[54px] lg:py-[72px]">
          <div className="flex flex-col lg:flex-row justify-center items-start w-full gap-8 lg:gap-0">
            {/* Main Content Container */}
            <div className="flex flex-col justify-start items-start w-full lg:w-[64%] gap-6 lg:gap-0">
              {/* Header Row */}
              <div className="flex flex-col lg:flex-row justify-between items-start w-full gap-6 lg:gap-0">
                {/* Company Title */}
                <div className="w-full lg:w-[28%]">
                  <h1 className="text-[16px] sm:text-[18px] lg:text-[20px] font-semibold leading-[24px] sm:leading-[26px] lg:leading-[28px] text-left text-white font-inter">
                    Trung Tâm Đào Tạo & Chăm Sóc Sức Khỏe OM'E Việt Nam
                  </h1>
                </div>
                {/* Policy Section and About Us */}
                <div className="flex flex-col lg:flex-row justify-start items-center w-full lg:w-[66%] gap-6 lg:gap-0">
                  {/* Policy Section */}
                  <div className="flex flex-col justify-start items-center w-full lg:flex-1 gap-4">
                    {/* Policy Header Row */}
                    <div className="flex flex-col sm:flex-row justify-start lg:justify-between items-start lg:items-center w-full gap-4 lg:gap-0">
                      <h2 className="text-[16px] sm:text-[18px] lg:text-[20px] font-semibold leading-[20px] sm:leading-[22px] lg:leading-[25px] text-left text-white font-inter w-auto">
                        Chính Sách Và Quy Định Chung
                      </h2>
                      <h3 className="text-[14px] sm:text-[16px] lg:text-[18px] font-bold leading-[18px] sm:leading-[20px] lg:leading-[22px] text-left text-white font-inter w-auto lg:ml-[62px]">
                        Về chúng tôi
                      </h3>
                    </div>
                    {/* Policy Links Row */}
                    <div className="flex flex-col sm:flex-row justify-between items-start w-full gap-4 lg:gap-0">
                      <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto mb-2">
                        Điều khoản sử dụng
                      </p>
                      <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto lg:mr-[161px]">
                        Trang chủ
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              {/* Contact Information and Policy Links */}
              <div className="flex flex-col justify-start items-start w-full gap-6 lg:gap-0 lg:-mt-[2px]">
                {/* First Row - Address and Privacy Policy */}
                <div className="flex flex-col lg:flex-row justify-start items-start lg:items-center w-full gap-4 lg:gap-0">
                  {/* Address */}
                  <div className="flex flex-row justify-start items-start w-auto gap-2">
                    <Image
                      src="/images/img_frame_white_a700.svg"
                      alt="Location icon"
                      width={24}
                      height={24}
                      className="w-[24px] h-[24px] mt-[6px]"
                    />
                    <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter">
                      116 Trần Vỹ, Mai Dịch, Cầu Giấy, Hà Nội
                    </p>
                  </div>
                  {/* Privacy Policy */}
                  <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto lg:ml-auto">
                    Chính sách bảo mật
                  </p>
                </div>
                {/* Second Row - Phone and Purchase Policy */}
                <div className="flex flex-col lg:flex-row justify-start items-start lg:items-center w-full gap-4 lg:gap-0">
                  {/* Phone */}
                  <div className="flex flex-row justify-start items-center w-auto gap-2 mb-1">
                    <Image
                      src="/images/img_frame_white_a700_24x24.svg"
                      alt="Phone icon"
                      width={24}
                      height={24}
                      className="w-[24px] h-[24px]"
                    />
                    <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter">
                      0966.000.643
                    </p>
                  </div>
                  {/* Purchase Policy */}
                  <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto lg:ml-auto">
                    Chính sách mua hàng và thanh toán
                  </p>
                </div>
                {/* Third Row - Email and Refund Policy */}
                <div className="flex flex-col lg:flex-row justify-start items-start lg:items-center w-full gap-4 lg:gap-0">
                  {/* Email */}
                  <div className="flex flex-row justify-start items-center w-auto gap-2">
                    <Image
                      src="/images/img_frame_24x24.svg"
                      alt="Email icon"
                      width={24}
                      height={24}
                      className="w-[24px] h-[24px]"
                    />
                    <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter">
                      <EMAIL>
                    </p>
                  </div>
                  {/* Refund Policy */}
                  <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto lg:ml-auto">
                    Chính sách hoàn tiền
                  </p>
                </div>
              </div>
              {/* About Us Links Column */}
              <div className="flex flex-col lg:flex-row justify-start items-start lg:items-end w-full gap-4 lg:gap-0">
                {/* Website and Complaint Policy */}
                <div className="flex flex-col lg:flex-row justify-start items-start lg:items-end w-full gap-4 lg:gap-0">
                  {/* Website */}
                  <div className="flex flex-row justify-start items-center w-auto gap-2 mt-2">
                    <Image
                      src="/images/img_frame_1.svg"
                      alt="Website icon"
                      width={24}
                      height={24}
                      className="w-[24px] h-[24px]"
                    />
                    <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter">
                      https://ome.edu.vn/
                    </p>
                  </div>
                  {/* Complaint Policy */}
                  <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto">
                    Chính sách khiếu nại
                  </p>
                  {/* Contact Link */}
                  <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto lg:ml-[135px]">
                    Liên hệ
                  </p>
                </div>
                {/* About Us Navigation */}
                <div className="flex flex-col justify-start items-start w-auto gap-4 lg:ml-auto">
                  <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter">
                    Giới thiệu
                  </p>
                  <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter">
                    Tin tức
                  </p>
                </div>
              </div>
              {/* Certification Badge */}
              <div className="mt-[14px]">
                <Image
                  src="/images/img_image_60x160.png"
                  alt="Certification badge"
                  width={160}
                  height={60}
                  className="w-[80px] sm:w-[120px] lg:w-[160px] h-[30px] sm:h-[45px] lg:h-[60px]"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default HomePage;
